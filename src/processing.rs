use std::fs::File;
use std::io::BufReader;
use struson::reader::JsonStreamReader;
use duckdb::core::{DataChunkHandle, Inserter};

use crate::types::{InferredJsonType, TempJsonValue};
use crate::schema::InferredSchema;
use crate::paths::{VectorPath, VectorCapacities, ProjectionPath};
use crate::json::create_json_reader;
use crate::vectors::{insert_json_value_with_capacities, parse_json_value_temp};

/// Process JSON data with pre-calculated capacities (second pass)
pub fn process_json_with_capacities(
    file_path: &str,
    schema: &InferredSchema,
    output: &DataChunkHandle,
) -> Result<usize, Box<dyn std::error::Error>> {
    // Open the file for data processing
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = create_json_reader(buf_reader);

    // Process based on root type
    match &schema.root_type {
        InferredJsonType::Array { element_type, .. } => {
            // Get the total number of elements from capacities
            let total_elements = schema.capacities.get_capacity(&VectorPath::root()).unwrap_or(0);
            output.set_len(total_elements);

            process_array_with_capacities(&mut json_reader, output, element_type, &schema.capacities)
        },
        InferredJsonType::Object { fields, .. } => {
            // Single object - one row
            output.set_len(1);
            process_object_with_capacities(&mut json_reader, output, fields, &schema.capacities)
        },
        _ => {
            // Single primitive - one row
            output.set_len(1);
            process_primitive_with_capacities(&mut json_reader, output, &schema.root_type)
        }
    }
}

/// Process array with pre-calculated capacities using cumulative offset tracking
pub fn process_array_with_capacities(
    json_reader: &mut JsonStreamReader<BufReader<File>>,
    output: &DataChunkHandle,
    element_type: &InferredJsonType,
    capacities: &VectorCapacities,
) -> Result<usize, Box<dyn std::error::Error>> {
    use struson::reader::JsonReader;

    eprintln!("DEBUG: Processing array with element_type: {:?}", element_type);

    json_reader.begin_array()?;
    let mut row_count = 0;
    let mut cumulative_offset = 0; // Track cumulative offset across all rows
    let mut cumulative_nested_row = 0; // Track cumulative nested row index

    match element_type {
        InferredJsonType::Object { fields, .. } => {
            // Array of objects - each object becomes a row
            while json_reader.has_next()? {
                let temp_value = parse_json_value_temp(json_reader)?;
                if let TempJsonValue::Object(obj_fields) = temp_value {
                    for (col_idx, (field_name, field_type)) in fields.iter().enumerate() {
                        let current_path = ProjectionPath::field(field_name);
                        if let Some(field_value) = obj_fields.get(field_name) {
                            insert_json_value_with_capacities(field_value, output, col_idx, row_count, field_type, capacities, &current_path, &mut cumulative_offset, &mut cumulative_nested_row)?;
                        } else {
                            crate::vectors::set_vector_null_by_type(output, col_idx, row_count, field_type);
                        }
                    }
                }
                row_count += 1;
            }
        },
        InferredJsonType::Array { element_type: _nested_element_type, .. } => {
            // Array of arrays - this is the 3D case!
            eprintln!("DEBUG: Processing nested arrays (3D case) with cumulative offset tracking");
            while json_reader.has_next()? {
                let temp_value = parse_json_value_temp(json_reader)?;
                // For nested arrays, we need to use the correct path that matches schema inference
                // The path should be [Root] for the current level, and the nested processing
                // will build the correct paths for deeper levels
                let current_path = ProjectionPath::root();
                eprintln!("DEBUG: Processing nested array element at row {}, path: {:?}, cumulative_offset: {}, cumulative_nested_row: {}",
                          row_count, current_path, cumulative_offset, cumulative_nested_row);
                insert_json_value_with_capacities(&temp_value, output, 0, row_count, element_type, capacities, &current_path, &mut cumulative_offset, &mut cumulative_nested_row)?;
                row_count += 1;
            }
        },
        _ => {
            // Array of primitives - each element becomes a row
            eprintln!("DEBUG: Processing primitive array");
            while json_reader.has_next()? {
                let temp_value = parse_json_value_temp(json_reader)?;
                let current_path = ProjectionPath::root();
                insert_json_value_with_capacities(&temp_value, output, 0, row_count, element_type, capacities, &current_path, &mut cumulative_offset, &mut cumulative_nested_row)?;
                row_count += 1;
            }
        }
    }

    json_reader.end_array()?;
    Ok(row_count)
}

/// Process object with pre-calculated capacities
pub fn process_object_with_capacities(
    json_reader: &mut JsonStreamReader<BufReader<File>>,
    output: &DataChunkHandle,
    fields: &[(String, InferredJsonType)],
    capacities: &VectorCapacities,
) -> Result<usize, Box<dyn std::error::Error>> {
    // Parse the entire object into TempJsonValue
    let temp_value = parse_json_value_temp(json_reader)?;

    if let TempJsonValue::Object(obj_fields) = temp_value {
        if fields.is_empty() {
            // Empty object - insert a value into the fallback column
            let flat_vector = output.flat_vector(0);
            flat_vector.insert(0, "{}"); // Insert empty object representation
        } else {
            for (col_idx, (field_name, field_type)) in fields.iter().enumerate() {
                let current_path = ProjectionPath::field(field_name);
                // Each field gets its own cumulative offset starting at 0
                let mut cumulative_offset = 0;
                let mut cumulative_nested_row = 0;

                if let Some(field_value) = obj_fields.get(field_name) {
                    insert_json_value_with_capacities(field_value, output, col_idx, 0, field_type, capacities, &current_path, &mut cumulative_offset, &mut cumulative_nested_row)?;
                } else {
                    crate::vectors::set_vector_null_by_type(output, col_idx, 0, field_type);
                }
            }
        }
    }

    Ok(1)
}

/// Process primitive with pre-calculated capacities
pub fn process_primitive_with_capacities(
    json_reader: &mut JsonStreamReader<BufReader<File>>,
    output: &DataChunkHandle,
    json_type: &InferredJsonType,
) -> Result<usize, Box<dyn std::error::Error>> {
    let temp_value = parse_json_value_temp(json_reader)?;
    let current_path = ProjectionPath::root();
    let capacities = VectorCapacities::new(); // Empty capacities for primitives
    let mut cumulative_offset = 0; // Primitives don't need cumulative offset tracking
    let mut cumulative_nested_row = 0; // Primitives don't need nested row tracking

    insert_json_value_with_capacities(&temp_value, output, 0, 0, json_type, &capacities, &current_path, &mut cumulative_offset, &mut cumulative_nested_row)?;

    Ok(1)
}

use std::fs::File;
use std::io::BufReader;
use struson::reader::JsonStreamReader;

use crate::types::{InferredJsonType, SchemaInferenceConfig};
use crate::paths::{VectorPath, VectorCapacities, PathComponent};
use crate::utils::create_json_reader;

/// Schema inference result with capacity information
#[derive(Debug, Clone)]
pub struct InferredSchema {
    pub root_type: InferredJsonType,
    pub capacities: VectorCapacities,
}

/// Two-pass JSON processor for efficient memory usage
pub struct TwoPassJsonProcessor {
    file_path: String,
    schema: InferredSchema,
}

impl TwoPassJsonProcessor {
    /// Create a new processor by analyzing the file (first pass)
    pub fn new(file_path: &str, config: SchemaInferenceConfig) -> Result<Self, Box<dyn std::error::Error>> {
        let schema = infer_schema_with_capacities(file_path, config)?;

        Ok(Self {
            file_path: file_path.to_string(),
            schema,
        })
    }

    /// Get the inferred schema
    pub fn schema(&self) -> &InferredSchema {
        &self.schema
    }

    /// Process the file data into DuckDB vectors (second pass)
    pub fn process_to_vectors(&self, output: &duckdb::core::DataChunkHandle) -> Result<usize, Box<dyn std::error::Error>> {
        crate::processing::process_json_with_capacities(&self.file_path, &self.schema, output)
    }
}

/// Schema inference with capacity calculation (first pass)
pub fn infer_schema_with_capacities(file_path: &str, config: SchemaInferenceConfig) -> Result<InferredSchema, Box<dyn std::error::Error>> {
    if config.enable_debug_output {
        eprintln!("SCHEMA INFERENCE: Starting two-pass analysis of file: {}", file_path);
    }

    // Open and parse the JSON file
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = create_json_reader(buf_reader);

    // Infer schema and calculate capacities in single pass
    let mut capacities = VectorCapacities::new();
    let root_type = infer_json_type_with_capacities(&mut json_reader, &config, &mut capacities, VectorPath::root())?;

    if config.enable_debug_output {
        eprintln!("SCHEMA INFERENCE: Detected root type: {:?}", root_type);
        eprintln!("SCHEMA INFERENCE: Calculated capacities: {:?}", capacities);
    }

    let schema = InferredSchema {
        root_type,
        capacities,
    };

    Ok(schema)
}

/// Legacy schema inference for backward compatibility
pub fn infer_schema_from_file(file_path: &str, config: SchemaInferenceConfig) -> Result<InferredSchema, Box<dyn std::error::Error>> {
    infer_schema_with_capacities(file_path, config)
}

/// Infer JSON type with capacity calculation
fn infer_json_type_with_capacities(
    json_reader: &mut JsonStreamReader<BufReader<File>>,
    config: &SchemaInferenceConfig,
    capacities: &mut VectorCapacities,
    current_path: VectorPath,
) -> Result<InferredJsonType, Box<dyn std::error::Error>> {
    use struson::reader::JsonReader;

    match json_reader.peek()? {
        struson::reader::ValueType::Array => {
            if config.enable_debug_output {
                eprintln!("SCHEMA INFERENCE: Found array at path: {}", current_path.to_string());
            }

            json_reader.begin_array()?;

            if !json_reader.has_next()? {
                // Empty array
                json_reader.end_array()?;
                capacities.set_capacity(current_path, 0);
                return Ok(InferredJsonType::Array {
                    element_type: Box::new(InferredJsonType::Null),
                });
            }

            // Count elements and infer type from first element
            let mut element_count = 0;
            let mut element_path = current_path.clone();
            element_path.path_components.push(PathComponent::ArrayElement);

            // Infer type from first element
            let element_type = infer_json_type_with_capacities(json_reader, config, capacities, element_path)?;
            element_count += 1;

            // Count remaining elements
            while json_reader.has_next()? {
                json_reader.skip_value()?;
                element_count += 1;
            }

            json_reader.end_array()?;

            // Set capacity for this array
            capacities.set_capacity(current_path, element_count);

            if config.enable_debug_output {
                eprintln!("SCHEMA INFERENCE: Array has {} elements", element_count);
            }

            Ok(InferredJsonType::Array {
                element_type: Box::new(element_type),
            })
        },

        struson::reader::ValueType::Object => {
            if config.enable_debug_output {
                eprintln!("SCHEMA INFERENCE: Found object at path: {}", current_path.to_string());
            }

            json_reader.begin_object()?;
            let mut fields = Vec::new();

            while json_reader.has_next()? {
                let field_name = json_reader.next_name()?.to_string();
                let mut field_path = current_path.clone();
                field_path.path_components.push(PathComponent::ObjectField(field_name.clone()));
                
                let field_type = infer_json_type_with_capacities(json_reader, config, capacities, field_path)?;
                fields.push((field_name, field_type));

                if config.enable_debug_output {
                    eprintln!("SCHEMA INFERENCE: Found field: {}", fields.last().unwrap().0);
                }
            }

            json_reader.end_object()?;

            Ok(InferredJsonType::Object { fields })
        },

        struson::reader::ValueType::String => {
            json_reader.next_string()?;
            Ok(InferredJsonType::String)
        },

        struson::reader::ValueType::Number => {
            json_reader.next_number_as_str()?;
            Ok(InferredJsonType::Number)
        },

        struson::reader::ValueType::Boolean => {
            json_reader.next_bool()?;
            Ok(InferredJsonType::Boolean)
        },

        struson::reader::ValueType::Null => {
            json_reader.next_null()?;
            Ok(InferredJsonType::Null)
        },
    }
}

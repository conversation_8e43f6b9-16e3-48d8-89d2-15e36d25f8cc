use std::collections::HashMap;
use std::fs::File;
use std::io::BufReader;
use struson::reader::JsonStreamReader;
use duckdb::core::{DataChunkHandle, Inserter, StructVector, ListVector};

use crate::types::{InferredJsonType, TempJsonValue};
use crate::paths::{VectorCapacities, ProjectionPath, PathSegment, projection_path_to_vector_path};

/// Parse JSON value into temporary representation for recursive processing
pub fn parse_json_value_temp(json_reader: &mut JsonStreamReader<BufReader<File>>) -> Result<TempJsonValue, Box<dyn std::error::Error>> {
    use struson::reader::JsonReader;

    match json_reader.peek()? {
        struson::reader::ValueType::Array => {
            json_reader.begin_array()?;
            let mut elements = Vec::new();
            while json_reader.has_next()? {
                elements.push(parse_json_value_temp(json_reader)?);
            }
            json_reader.end_array()?;
            Ok(TempJsonValue::Array(elements))
        },
        struson::reader::ValueType::Object => {
            json_reader.begin_object()?;
            let mut fields = HashMap::new();
            while json_reader.has_next()? {
                let field_name = json_reader.next_name()?.to_string();
                let field_value = parse_json_value_temp(json_reader)?;
                fields.insert(field_name, field_value);
            }
            json_reader.end_object()?;
            Ok(TempJsonValue::Object(fields))
        },
        struson::reader::ValueType::String => {
            let s = json_reader.next_string()?;
            Ok(TempJsonValue::String(s.to_string()))
        },
        struson::reader::ValueType::Number => {
            let num_str = json_reader.next_number_as_str()?;
            let num: f64 = num_str.parse().unwrap_or(0.0);
            Ok(TempJsonValue::Number(num))
        },
        struson::reader::ValueType::Boolean => {
            let b = json_reader.next_bool()?;
            Ok(TempJsonValue::Boolean(b))
        },
        struson::reader::ValueType::Null => {
            json_reader.next_null()?;
            Ok(TempJsonValue::Null)
        },
    }
}

/// Insert JSON value with capacity management and cumulative offset tracking
pub fn insert_json_value_with_capacities(
    value: &TempJsonValue,
    output: &DataChunkHandle,
    col_idx: usize,
    row_index: usize,
    expected_type: &InferredJsonType,
    capacities: &VectorCapacities,
    current_path: &ProjectionPath,
    cumulative_offset: &mut usize,
    cumulative_nested_row: &mut usize,
) -> Result<(), Box<dyn std::error::Error>> {
    match (value, expected_type) {
        (TempJsonValue::Array(elements), InferredJsonType::Array { element_type, .. }) => {
            insert_array_with_exact_capacities(elements, output, col_idx, row_index, element_type, capacities, current_path, cumulative_offset, cumulative_nested_row)?;
        },
        (TempJsonValue::Object(fields), InferredJsonType::Object { fields: expected_fields, .. }) => {
            insert_object_with_exact_capacities(fields, output, col_idx, row_index, expected_fields, capacities, current_path)?;
            // Objects don't affect cumulative offset for arrays
        },
        (TempJsonValue::Number(num), InferredJsonType::Number) => {
            let mut flat_vector = output.flat_vector(col_idx);
            let slice = flat_vector.as_mut_slice::<f64>();
            if row_index < slice.len() {
                slice[row_index] = *num;
            }
            // Primitives don't affect cumulative offset for arrays
        },
        (TempJsonValue::String(s), InferredJsonType::String) => {
            let flat_vector = output.flat_vector(col_idx);
            flat_vector.insert(row_index, s.as_str());
            // Primitives don't affect cumulative offset for arrays
        },
        (TempJsonValue::Boolean(b), InferredJsonType::Boolean) => {
            let mut flat_vector = output.flat_vector(col_idx);
            let slice = flat_vector.as_mut_slice::<bool>();
            if row_index < slice.len() {
                slice[row_index] = *b;
            }
            // Primitives don't affect cumulative offset for arrays
        },
        (TempJsonValue::Null, _) => {
            set_vector_null_by_type(output, col_idx, row_index, expected_type);
            // Null values don't affect cumulative offset for arrays
        },
        _ => {
            // Type mismatch - set as null
            set_vector_null_by_type(output, col_idx, row_index, expected_type);
            // Type mismatches don't affect cumulative offset for arrays
        }
    }
    Ok(())
}

/// Set vector null based on type
pub fn set_vector_null_by_type(
    output: &DataChunkHandle,
    col_idx: usize,
    row_index: usize,
    json_type: &InferredJsonType,
) {
    match json_type {
        InferredJsonType::Array { .. } => {
            let mut list_vector = output.list_vector(col_idx);
            list_vector.set_null(row_index);
        },
        InferredJsonType::Object { .. } => {
            let mut struct_vector = output.struct_vector(col_idx);
            struct_vector.set_null(row_index);
        },
        InferredJsonType::String => {
            let mut flat_vector = output.flat_vector(col_idx);
            flat_vector.set_null(row_index);
        },
        InferredJsonType::Number => {
            let mut flat_vector = output.flat_vector(col_idx);
            flat_vector.set_null(row_index);
        },
        InferredJsonType::Boolean => {
            let mut flat_vector = output.flat_vector(col_idx);
            flat_vector.set_null(row_index);
        },
        InferredJsonType::Null => {
            let mut flat_vector = output.flat_vector(col_idx);
            flat_vector.set_null(row_index);
        },
    }
}

/// Insert object with exact pre-calculated capacities
pub fn insert_object_with_exact_capacities(
    fields: &HashMap<String, TempJsonValue>,
    output: &DataChunkHandle,
    col_idx: usize,
    row_index: usize,
    expected_fields: &[(String, InferredJsonType)],
    capacities: &VectorCapacities,
    current_path: &ProjectionPath,
) -> Result<(), Box<dyn std::error::Error>> {
    // Get the struct vector for this column
    let mut struct_vector = output.struct_vector(col_idx);

    // Process each field in the struct
    for (field_idx, (field_name, field_type)) in expected_fields.iter().enumerate() {
        let field_path = current_path.append(PathSegment::Field(field_name.clone()));
        let mut cumulative_offset = 0; // Objects don't need cumulative offset tracking
        let mut cumulative_nested_row = 0; // Objects don't need nested row tracking

        if let Some(field_value) = fields.get(field_name) {
            // Handle different field types using the appropriate child vector
            match field_type {
                InferredJsonType::Object { fields: nested_fields, .. } => {
                    // Nested struct - get the struct child vector
                    let child_struct_vector = struct_vector.struct_vector_child(field_idx);

                    // Create a temporary DataChunkHandle wrapper for the child struct
                    // For now, we'll recursively call with the child struct
                    if let TempJsonValue::Object(nested_obj_fields) = field_value {
                        insert_object_with_exact_capacities_struct(
                            nested_obj_fields,
                            &child_struct_vector,
                            row_index,
                            nested_fields,
                            capacities,
                            &field_path,
                        )?;
                    }
                },
                InferredJsonType::Array { .. } => {
                    // Array field - get the list child vector
                    let mut child_list_vector = struct_vector.list_vector_child(field_idx);

                    // Handle array insertion (this needs more work)
                    if let TempJsonValue::Array(array_elements) = field_value {

                        insert_array_with_exact_capacities_list(
                            array_elements,
                            &mut child_list_vector,
                            row_index,
                            field_type,
                            capacities,
                            &field_path,
                            &mut cumulative_offset,
                            &mut cumulative_nested_row,
                        )?;
                    }
                },
                _ => {
                    // Primitive field - get the flat child vector
                    // We need to determine capacity for primitive fields
                    let capacity = 1; // For single object, capacity is 1
                    let mut child_flat_vector = struct_vector.child(field_idx, capacity);

                    // Insert primitive value
                    insert_primitive_with_exact_capacity(field_value, &mut child_flat_vector, row_index, field_type)?;
                }
            }
        } else {
            // Field is missing - set as null
            set_struct_field_null(&mut struct_vector, field_idx, row_index, field_type);
        }
    }

    Ok(())
}

/// Insert object into a struct vector (helper for nested structs)
fn insert_object_with_exact_capacities_struct(
    fields: &HashMap<String, TempJsonValue>,
    struct_vector: &StructVector,
    row_index: usize,
    expected_fields: &[(String, InferredJsonType)],
    capacities: &VectorCapacities,
    current_path: &ProjectionPath,
) -> Result<(), Box<dyn std::error::Error>> {
    // Process each field in the nested struct
    for (field_idx, (field_name, field_type)) in expected_fields.iter().enumerate() {
        let field_path = current_path.append(PathSegment::Field(field_name.clone()));

        if let Some(field_value) = fields.get(field_name) {
            match field_type {
                InferredJsonType::Object { fields: nested_fields, .. } => {
                    // Recursively nested struct
                    let child_struct_vector = struct_vector.struct_vector_child(field_idx);
                    if let TempJsonValue::Object(nested_obj_fields) = field_value {
                        insert_object_with_exact_capacities_struct(
                            nested_obj_fields,
                            &child_struct_vector,
                            row_index,
                            nested_fields,
                            capacities,
                            &field_path,
                        )?;
                    }
                },
                InferredJsonType::Array { .. } => {
                    // Array field in nested struct
                    let mut child_list_vector = struct_vector.list_vector_child(field_idx);
                    if let TempJsonValue::Array(array_elements) = field_value {
                        let mut cumulative_offset = 0;
                        let mut cumulative_nested_row = 0;
                        insert_array_with_exact_capacities_list(
                            array_elements,
                            &mut child_list_vector,
                            row_index,
                            field_type,
                            capacities,
                            &field_path,
                            &mut cumulative_offset,
                            &mut cumulative_nested_row,
                        )?;
                    }
                },
                _ => {
                    // Primitive field in nested struct
                    let capacity = 1;
                    let mut child_flat_vector = struct_vector.child(field_idx, capacity);
                    insert_primitive_with_exact_capacity(field_value, &mut child_flat_vector, row_index, field_type)?;
                }
            }
        } else {
            // Field is missing - set as null
            set_struct_field_null_direct(struct_vector, field_idx, row_index, field_type);
        }
    }
    Ok(())
}

/// Insert array into a list vector (helper for arrays in structs)
fn insert_array_with_exact_capacities_list(
    elements: &[TempJsonValue],
    list_vector: &mut ListVector,
    row_index: usize,
    element_type: &InferredJsonType,
    _capacities: &VectorCapacities,
    _current_path: &ProjectionPath,
    cumulative_offset: &mut usize,
    _cumulative_nested_row: &mut usize,
) -> Result<(), Box<dyn std::error::Error>> {
    eprintln!("DEBUG: insert_array_with_exact_capacities_list called with row_index={}, element_type={:?}, cumulative_offset={}", row_index, element_type, cumulative_offset);

    let element_count = elements.len();

    if element_count == 0 {
        list_vector.set_entry(row_index, 0, 0);
        return Ok(());
    }

    // Use the current cumulative offset
    let row_offset = *cumulative_offset;

    match element_type {
        InferredJsonType::Array { element_type: nested_element_type, .. } => {
            // Nested arrays - this case should handle arrays of arrays
            eprintln!("DEBUG: Handling nested array case - this should not be reached for 4D+ arrays after the fix");
            let _nested_list_vector = list_vector.list_child();
            // Simplified implementation - this should not be reached anymore
            list_vector.set_entry(row_index, row_offset, element_count);
            *cumulative_offset += element_count;
        },
        InferredJsonType::Object { fields: struct_fields, .. } => {
            // Array of structs
            let struct_child_vector = list_vector.struct_child(element_count);

            for (elem_idx, element) in elements.iter().enumerate() {
                if let TempJsonValue::Object(obj_fields) = element {
                    let struct_row_index = row_offset + elem_idx;

                    // Process each field of the struct
                    for (field_idx, (field_name, field_type)) in struct_fields.iter().enumerate() {
                        if let Some(field_value) = obj_fields.get(field_name) {
                            match field_type {
                                InferredJsonType::Object { fields: nested_fields, .. } => {
                                    // Nested struct within struct
                                    let child_struct_vector = struct_child_vector.struct_vector_child(field_idx);
                                    if let TempJsonValue::Object(nested_obj_fields) = field_value {
                                        insert_object_with_exact_capacities_struct(
                                            nested_obj_fields,
                                            &child_struct_vector,
                                            struct_row_index,
                                            nested_fields,
                                            _capacities,
                                            _current_path,
                                        )?;
                                    }
                                },
                                InferredJsonType::Array { .. } => {
                                    // Array field within struct - recursive call
                                    let mut child_list_vector = struct_child_vector.list_vector_child(field_idx);
                                    if let TempJsonValue::Array(array_elements) = field_value {
                                        let mut field_cumulative_offset = 0;
                                        let mut field_cumulative_nested_row = 0;
                                        insert_array_with_exact_capacities_list(
                                            array_elements,
                                            &mut child_list_vector,
                                            struct_row_index,
                                            field_type,
                                            _capacities,
                                            _current_path,
                                            &mut field_cumulative_offset,
                                            &mut field_cumulative_nested_row,
                                        )?;
                                    }
                                },
                                _ => {
                                    // Primitive field within struct
                                    let capacity = element_count;
                                    let mut child_flat_vector = struct_child_vector.child(field_idx, capacity);
                                    insert_primitive_with_exact_capacity(field_value, &mut child_flat_vector, struct_row_index, field_type)?;
                                }
                            }
                        } else {
                            // Field is missing - set as null
                            set_struct_field_null_direct(&struct_child_vector, field_idx, struct_row_index, field_type);
                        }
                    }
                }
            }

            list_vector.set_entry(row_index, row_offset, element_count);
            *cumulative_offset += element_count;
        },
        _ => {
            // Primitives
            eprintln!("DEBUG: Handling primitives in insert_array_with_exact_capacities_list, element_count={}, row_offset={}", element_count, row_offset);
            let total_capacity = element_count; // Simplified capacity calculation
            let mut flat_vector = list_vector.child(total_capacity);

            for (elem_idx, element) in elements.iter().enumerate() {
                eprintln!("DEBUG: Inserting primitive element {} at offset {}: {:?}", elem_idx, row_offset + elem_idx, element);
                insert_primitive_with_exact_capacity(element, &mut flat_vector, row_offset + elem_idx, element_type)?;
            }

            list_vector.set_entry(row_index, row_offset, element_count);
            *cumulative_offset += element_count;
        }
    }

    Ok(())
}

/// Set a struct field to null
fn set_struct_field_null(
    struct_vector: &mut StructVector,
    field_idx: usize,
    row_index: usize,
    field_type: &InferredJsonType,
) {
    set_struct_field_null_direct(struct_vector, field_idx, row_index, field_type);
}

/// Set a struct field to null (direct version)
fn set_struct_field_null_direct(
    struct_vector: &StructVector,
    field_idx: usize,
    row_index: usize,
    field_type: &InferredJsonType,
) {
    match field_type {
        InferredJsonType::Array { .. } => {
            let mut child_list_vector = struct_vector.list_vector_child(field_idx);
            child_list_vector.set_null(row_index);
        },
        InferredJsonType::Object { .. } => {
            let mut child_struct_vector = struct_vector.struct_vector_child(field_idx);
            child_struct_vector.set_null(row_index);
        },
        _ => {
            let capacity = 1;
            let mut child_flat_vector = struct_vector.child(field_idx, capacity);
            child_flat_vector.set_null(row_index);
        }
    }
}

/// Insert array with exact pre-calculated capacities using cumulative offset tracking
pub fn insert_array_with_exact_capacities(
    elements: &[TempJsonValue],
    output: &DataChunkHandle,
    col_idx: usize,
    row_index: usize,
    element_type: &InferredJsonType,
    capacities: &VectorCapacities,
    current_path: &ProjectionPath,
    cumulative_offset: &mut usize,
    cumulative_nested_row: &mut usize, // Track cumulative nested row index
) -> Result<(), Box<dyn std::error::Error>> {
    let mut list_vector = output.list_vector(col_idx);
    let element_count = elements.len();

    if element_count == 0 {
        list_vector.set_entry(row_index, 0, 0);
        return Ok(());
    }

    // Use the current cumulative offset directly - no formula calculation needed
    let row_offset = *cumulative_offset;

    eprintln!("DEBUG: Processing nested arrays (3D case) with cumulative offset: {}", row_offset);

    match element_type {
        InferredJsonType::Array { element_type: nested_element_type, .. } => {
            // Nested arrays with exact capacity
            let mut nested_list_vector = list_vector.list_child();

            // Build the correct path for nested array capacity lookup
            let mut nested_path = current_path.clone();
            nested_path.segments.push(PathSegment::ArrayIndex(0)); // Use index 0 as representative
            eprintln!("DEBUG: Looking up nested capacity for path: {:?}", nested_path);
            let nested_capacity_per_row = get_exact_nested_capacity(capacities, &nested_path)?;

            // Calculate total capacity needed - for irregular arrays, we need to account for
            // the actual cumulative usage, not just uniform assumptions
            let root_capacity = capacities.get_capacity(&projection_path_to_vector_path(current_path)).unwrap_or(1);
            let uniform_capacity = root_capacity * nested_capacity_per_row;

            // Calculate actual capacity needed based on current usage plus this row's elements
            let elements_in_this_row: usize = elements.iter()
                .map(|elem| if let TempJsonValue::Array(arr) = elem { arr.len() } else { 0 })
                .sum();
            let actual_capacity_needed = row_offset + elements_in_this_row;

            // Use the larger of uniform calculation or actual need
            let total_nested_capacity = uniform_capacity.max(actual_capacity_needed);

            eprintln!("DEBUG: Nested capacity calculation: nested_capacity_per_row={}, root_capacity={}, uniform_capacity={}, actual_needed={}, total_nested_capacity={}",
                      nested_capacity_per_row, root_capacity, uniform_capacity, actual_capacity_needed, total_nested_capacity);

            if nested_list_vector.len() < total_nested_capacity {
                nested_list_vector.set_len(total_nested_capacity);
            }

            let mut current_offset = row_offset;
            // CRITICAL FIX: Use cumulative nested row tracking instead of formula
            let nested_row_start = *cumulative_nested_row;

            for (elem_idx, element) in elements.iter().enumerate() {
                if let TempJsonValue::Array(nested_elements) = element {
                    eprintln!("DEBUG: Setting nested list entry: nested_row_index={}, current_offset={}, length={}",
                              nested_row_start + elem_idx, current_offset, nested_elements.len());
                    nested_list_vector.set_entry(nested_row_start + elem_idx, current_offset, nested_elements.len());

                    // Insert each nested element
                    for (nested_idx, nested_element) in nested_elements.iter().enumerate() {
                        eprintln!("DEBUG: Inserting nested element {} at offset {}: {:?}",
                                  nested_idx, current_offset + nested_idx, nested_element);

                        // Check if the nested element is itself an array (for 4D+ arrays)
                        match nested_element_type.as_ref() {
                            InferredJsonType::Array { .. } => {
                                // Recursive case: nested element is an array
                                if let TempJsonValue::Array(sub_elements) = nested_element {
                                    eprintln!("DEBUG: 4D+ array case - recursively calling insert_array_with_exact_capacities");

                                    // For 4D+ arrays, we need to recursively call the main function
                                    // with proper cumulative offset tracking
                                    let mut sub_cumulative_offset = current_offset;
                                    let mut sub_cumulative_nested_row = nested_row_start + elem_idx;

                                    // Extract the correct element type for the recursive call
                                    let sub_element_type = match nested_element_type.as_ref() {
                                        InferredJsonType::Array { element_type, .. } => element_type,
                                        _ => nested_element_type, // Fallback, shouldn't happen
                                    };

                                    insert_array_with_exact_capacities_list(
                                        sub_elements,
                                        &mut nested_list_vector,
                                        nested_row_start + elem_idx,
                                        sub_element_type,
                                        capacities,
                                        current_path,
                                        &mut sub_cumulative_offset,
                                        &mut sub_cumulative_nested_row,
                                    )?;

                                    // Update the main cumulative offset
                                    current_offset = sub_cumulative_offset;
                                }
                            },
                            _ => {
                                // Base case: nested element is a primitive
                                insert_primitive_with_exact_capacity(nested_element, &mut nested_list_vector.child(total_nested_capacity), current_offset, nested_element_type)?;
                                current_offset += 1;
                            }
                        }
                    }

                    current_offset += nested_elements.len();
                } else {
                    nested_list_vector.set_null(nested_row_start + elem_idx);
                    current_offset += 1;
                }
            }

            // Set the entry for this row in the parent list using cumulative nested row tracking
            eprintln!("DEBUG: Setting list entry: row_index={}, nested_row_offset={}, nested_arrays_in_row={}",
                      row_index, nested_row_start, element_count);
            list_vector.set_entry(row_index, nested_row_start, element_count);

            // Update cumulative offsets with actual elements consumed
            *cumulative_offset = current_offset;
            *cumulative_nested_row += element_count; // Update nested row counter

        },
        InferredJsonType::Object { fields: struct_fields, .. } => {
            // Array of structs - each element is a struct
            let struct_child_vector = list_vector.struct_child(element_count);

            for (elem_idx, element) in elements.iter().enumerate() {
                if let TempJsonValue::Object(obj_fields) = element {
                    // Insert each struct at the appropriate offset
                    let struct_row_index = row_offset + elem_idx;

                    // Process each field of the struct
                    for (field_idx, (field_name, field_type)) in struct_fields.iter().enumerate() {
                        let field_path = current_path.append(PathSegment::Field(field_name.clone()));

                        if let Some(field_value) = obj_fields.get(field_name) {
                            match field_type {
                                InferredJsonType::Object { fields: nested_fields, .. } => {
                                    // Nested struct within struct
                                    let child_struct_vector = struct_child_vector.struct_vector_child(field_idx);
                                    if let TempJsonValue::Object(nested_obj_fields) = field_value {
                                        insert_object_with_exact_capacities_struct(
                                            nested_obj_fields,
                                            &child_struct_vector,
                                            struct_row_index,
                                            nested_fields,
                                            capacities,
                                            &field_path,
                                        )?;
                                    }
                                },
                                InferredJsonType::Array { .. } => {
                                    // Array field within struct
                                    let mut child_list_vector = struct_child_vector.list_vector_child(field_idx);
                                    if let TempJsonValue::Array(array_elements) = field_value {
                                        let mut field_cumulative_offset = 0;
                                        let mut field_cumulative_nested_row = 0;
                                        insert_array_with_exact_capacities_list(
                                            array_elements,
                                            &mut child_list_vector,
                                            struct_row_index,
                                            field_type,
                                            capacities,
                                            &field_path,
                                            &mut field_cumulative_offset,
                                            &mut field_cumulative_nested_row,
                                        )?;
                                    }
                                },
                                _ => {
                                    // Primitive field within struct
                                    let capacity = element_count; // Capacity for all struct elements
                                    let mut child_flat_vector = struct_child_vector.child(field_idx, capacity);
                                    insert_primitive_with_exact_capacity(field_value, &mut child_flat_vector, struct_row_index, field_type)?;
                                }
                            }
                        } else {
                            // Field is missing - set as null
                            set_struct_field_null_direct(&struct_child_vector, field_idx, struct_row_index, field_type);
                        }
                    }
                }
            }

            // Set the entry in the parent list vector
            list_vector.set_entry(row_index, row_offset, element_count);

            // Update cumulative offset
            *cumulative_offset += element_count;
        },
        _ => {
            // Primitives with exact capacity
            let total_capacity = get_exact_primitive_capacity(capacities, current_path)?;
            let mut flat_vector = list_vector.child(total_capacity);

            for (elem_idx, element) in elements.iter().enumerate() {
                insert_primitive_with_exact_capacity(element, &mut flat_vector, row_offset + elem_idx, element_type)?;
            }

            // Update cumulative offset with actual elements consumed
            *cumulative_offset += element_count;

        }
    };

    // For non-nested arrays, set the entry in the parent list
    match element_type {
        InferredJsonType::Array { .. } => {
            // Already handled above in the nested array case
        },
        _ => {
            // For primitive arrays, set entry with the row offset and count
            list_vector.set_entry(row_index, row_offset, element_count);
        }
    };

    Ok(())
}

/// Get exact nested capacity for a given path
pub fn get_exact_nested_capacity(capacities: &VectorCapacities, nested_path: &ProjectionPath) -> Result<usize, Box<dyn std::error::Error>> {
    let vector_path = projection_path_to_vector_path(nested_path);
    capacities.get_capacity(&vector_path)
        .ok_or_else(|| format!("Could not find nested capacity for path: {:?}", vector_path).into())
}

/// Get exact primitive capacity for a given path
pub fn get_exact_primitive_capacity(capacities: &VectorCapacities, current_path: &ProjectionPath) -> Result<usize, Box<dyn std::error::Error>> {
    let vector_path = projection_path_to_vector_path(current_path);
    capacities.get_capacity(&vector_path)
        .ok_or_else(|| format!("Could not find primitive capacity for path: {:?}", vector_path).into())
}


/// Insert primitive value with exact capacity
pub fn insert_primitive_with_exact_capacity(
    value: &TempJsonValue,
    flat_vector: &mut duckdb::core::FlatVector,
    index: usize,
    expected_type: &InferredJsonType,
) -> Result<(), Box<dyn std::error::Error>> {
    match (value, expected_type) {
        (TempJsonValue::Number(num), InferredJsonType::Number) => {
            let slice = flat_vector.as_mut_slice::<f64>();
            if index < slice.len() {
                slice[index] = *num;
            }
        },
        (TempJsonValue::String(s), InferredJsonType::String) => {
            flat_vector.insert(index, s.as_str());
        },
        (TempJsonValue::Boolean(b), InferredJsonType::Boolean) => {
            let slice = flat_vector.as_mut_slice::<bool>();
            if index < slice.len() {
                slice[index] = *b;
            }
        },
        (TempJsonValue::Null, _) => {
            flat_vector.set_null(index);
        },
        _ => {
            // Type mismatch - set as null
            flat_vector.set_null(index);
        }
    }
    Ok(())
}

use std::collections::HashMap;
use std::fs::File;
use std::io::BufReader;
use struson::reader::{J<PERSON>Reader, JsonStreamReader};
use duckdb::core::{DataChunkHandle, Inserter};

use crate::types::{InferredJsonType, TempJsonValue};
use crate::paths::{VectorCapacities, ProjectionPath, PathSegment, projection_path_to_vector_path};

/// Parse JSON value into temporary representation for recursive processing
pub fn parse_json_value_temp(json_reader: &mut JsonStreamReader<BufReader<File>>) -> Result<TempJsonValue, Box<dyn std::error::Error>> {
    use struson::reader::JsonReader;

    match json_reader.peek()? {
        struson::reader::ValueType::Array => {
            json_reader.begin_array()?;
            let mut elements = Vec::new();
            while json_reader.has_next()? {
                elements.push(parse_json_value_temp(json_reader)?);
            }
            json_reader.end_array()?;
            Ok(TempJsonValue::Array(elements))
        },
        struson::reader::ValueType::Object => {
            json_reader.begin_object()?;
            let mut fields = HashMap::new();
            while json_reader.has_next()? {
                let field_name = json_reader.next_name()?.to_string();
                let field_value = parse_json_value_temp(json_reader)?;
                fields.insert(field_name, field_value);
            }
            json_reader.end_object()?;
            Ok(TempJsonValue::Object(fields))
        },
        struson::reader::ValueType::String => {
            let s = json_reader.next_string()?;
            Ok(TempJsonValue::String(s.to_string()))
        },
        struson::reader::ValueType::Number => {
            let num_str = json_reader.next_number_as_str()?;
            let num: f64 = num_str.parse().unwrap_or(0.0);
            Ok(TempJsonValue::Number(num))
        },
        struson::reader::ValueType::Boolean => {
            let b = json_reader.next_bool()?;
            Ok(TempJsonValue::Boolean(b))
        },
        struson::reader::ValueType::Null => {
            json_reader.next_null()?;
            Ok(TempJsonValue::Null)
        },
    }
}

/// Insert JSON value with capacity management and cumulative offset tracking
pub fn insert_json_value_with_capacities(
    value: &TempJsonValue,
    output: &DataChunkHandle,
    col_idx: usize,
    row_index: usize,
    expected_type: &InferredJsonType,
    capacities: &VectorCapacities,
    current_path: &ProjectionPath,
    cumulative_offset: &mut usize,
    cumulative_nested_row: &mut usize,
) -> Result<(), Box<dyn std::error::Error>> {
    match (value, expected_type) {
        (TempJsonValue::Array(elements), InferredJsonType::Array { element_type, .. }) => {
            insert_array_with_exact_capacities(elements, output, col_idx, row_index, element_type, capacities, current_path, cumulative_offset, cumulative_nested_row)?;
        },
        (TempJsonValue::Object(fields), InferredJsonType::Object { fields: expected_fields, .. }) => {
            insert_object_with_exact_capacities(fields, output, col_idx, row_index, expected_fields, capacities, current_path)?;
            // Objects don't affect cumulative offset for arrays
        },
        (TempJsonValue::Number(num), InferredJsonType::Number) => {
            let mut flat_vector = output.flat_vector(col_idx);
            let slice = flat_vector.as_mut_slice::<f64>();
            if row_index < slice.len() {
                slice[row_index] = *num;
            }
            // Primitives don't affect cumulative offset for arrays
        },
        (TempJsonValue::String(s), InferredJsonType::String) => {
            let mut flat_vector = output.flat_vector(col_idx);
            flat_vector.insert(row_index, s.as_str());
            // Primitives don't affect cumulative offset for arrays
        },
        (TempJsonValue::Boolean(b), InferredJsonType::Boolean) => {
            let mut flat_vector = output.flat_vector(col_idx);
            let slice = flat_vector.as_mut_slice::<bool>();
            if row_index < slice.len() {
                slice[row_index] = *b;
            }
            // Primitives don't affect cumulative offset for arrays
        },
        (TempJsonValue::Null, _) => {
            set_vector_null_by_type(output, col_idx, row_index, expected_type);
            // Null values don't affect cumulative offset for arrays
        },
        _ => {
            // Type mismatch - set as null
            set_vector_null_by_type(output, col_idx, row_index, expected_type);
            // Type mismatches don't affect cumulative offset for arrays
        }
    }
    Ok(())
}

/// Set vector null based on type
pub fn set_vector_null_by_type(
    output: &DataChunkHandle,
    col_idx: usize,
    row_index: usize,
    json_type: &InferredJsonType,
) {
    match json_type {
        InferredJsonType::Array { .. } => {
            let mut list_vector = output.list_vector(col_idx);
            list_vector.set_null(row_index);
        },
        InferredJsonType::Object { .. } => {
            let mut struct_vector = output.struct_vector(col_idx);
            struct_vector.set_null(row_index);
        },
        InferredJsonType::String => {
            let mut flat_vector = output.flat_vector(col_idx);
            flat_vector.set_null(row_index);
        },
        InferredJsonType::Number => {
            let mut flat_vector = output.flat_vector(col_idx);
            flat_vector.set_null(row_index);
        },
        InferredJsonType::Boolean => {
            let mut flat_vector = output.flat_vector(col_idx);
            flat_vector.set_null(row_index);
        },
        InferredJsonType::Null => {
            let mut flat_vector = output.flat_vector(col_idx);
            flat_vector.set_null(row_index);
        },
    }
}

/// Insert object with exact pre-calculated capacities
pub fn insert_object_with_exact_capacities(
    fields: &HashMap<String, TempJsonValue>,
    output: &DataChunkHandle,
    col_idx: usize,
    row_index: usize,
    expected_fields: &[(String, InferredJsonType)],
    capacities: &VectorCapacities,
    current_path: &ProjectionPath,
) -> Result<(), Box<dyn std::error::Error>> {
    // For struct vectors, we need to handle each field separately
    // This is a simplified implementation - in practice, struct handling is more complex
    for (field_idx, (field_name, field_type)) in expected_fields.iter().enumerate() {
        let field_path = current_path.append(PathSegment::Field(field_name.clone()));
        let mut cumulative_offset = 0; // Objects don't need cumulative offset tracking
        let mut cumulative_nested_row = 0; // Objects don't need nested row tracking

        if let Some(field_value) = fields.get(field_name) {
            // For now, we'll handle struct fields as separate columns
            // This is a simplification - proper struct handling would require more complex logic
            insert_json_value_with_capacities(
                field_value,
                output,
                field_idx, // Use field index as column index
                row_index,
                field_type,
                capacities,
                &field_path,
                &mut cumulative_offset,
                &mut cumulative_nested_row,
            )?;
        } else {
            set_vector_null_by_type(output, field_idx, row_index, field_type);
        }
    }

    Ok(())
}

/// Insert array with exact pre-calculated capacities using cumulative offset tracking
pub fn insert_array_with_exact_capacities(
    elements: &[TempJsonValue],
    output: &DataChunkHandle,
    col_idx: usize,
    row_index: usize,
    element_type: &InferredJsonType,
    capacities: &VectorCapacities,
    current_path: &ProjectionPath,
    cumulative_offset: &mut usize,
    cumulative_nested_row: &mut usize, // Track cumulative nested row index
) -> Result<(), Box<dyn std::error::Error>> {
    let mut list_vector = output.list_vector(col_idx);
    let element_count = elements.len();

    if element_count == 0 {
        list_vector.set_entry(row_index, 0, 0);
        return Ok(());
    }

    // Use the current cumulative offset directly - no formula calculation needed
    let row_offset = *cumulative_offset;

    eprintln!("DEBUG: Processing nested arrays (3D case) with cumulative offset: {}", row_offset);

    match element_type {
        InferredJsonType::Array { element_type: nested_element_type, .. } => {
            // Nested arrays with exact capacity
            let mut nested_list_vector = list_vector.list_child();

            // Build the correct path for nested array capacity lookup
            let mut nested_path = current_path.clone();
            nested_path.segments.push(PathSegment::ArrayIndex(0)); // Use index 0 as representative
            eprintln!("DEBUG: Looking up nested capacity for path: {:?}", nested_path);
            let nested_capacity_per_row = get_exact_nested_capacity(capacities, &nested_path)?;

            // Calculate total capacity needed - for irregular arrays, we need to account for
            // the actual cumulative usage, not just uniform assumptions
            let root_capacity = capacities.get_capacity(&projection_path_to_vector_path(current_path)).unwrap_or(1);
            let uniform_capacity = root_capacity * nested_capacity_per_row;

            // Calculate actual capacity needed based on current usage plus this row's elements
            let elements_in_this_row: usize = elements.iter()
                .map(|elem| if let TempJsonValue::Array(arr) = elem { arr.len() } else { 0 })
                .sum();
            let actual_capacity_needed = row_offset + elements_in_this_row;

            // Use the larger of uniform calculation or actual need
            let total_nested_capacity = uniform_capacity.max(actual_capacity_needed);

            eprintln!("DEBUG: Nested capacity calculation: nested_capacity_per_row={}, root_capacity={}, uniform_capacity={}, actual_needed={}, total_nested_capacity={}",
                      nested_capacity_per_row, root_capacity, uniform_capacity, actual_capacity_needed, total_nested_capacity);

            if nested_list_vector.len() < total_nested_capacity {
                nested_list_vector.set_len(total_nested_capacity);
            }

            let mut current_offset = row_offset;
            // CRITICAL FIX: Use cumulative nested row tracking instead of formula
            let nested_row_start = *cumulative_nested_row;

            for (elem_idx, element) in elements.iter().enumerate() {
                if let TempJsonValue::Array(nested_elements) = element {
                    eprintln!("DEBUG: Setting nested list entry: nested_row_index={}, current_offset={}, length={}",
                              nested_row_start + elem_idx, current_offset, nested_elements.len());
                    nested_list_vector.set_entry(nested_row_start + elem_idx, current_offset, nested_elements.len());

                    // Insert each nested element
                    for (nested_idx, nested_element) in nested_elements.iter().enumerate() {
                        eprintln!("DEBUG: Inserting nested element {} at offset {}: {:?}",
                                  nested_idx, current_offset + nested_idx, nested_element);
                        insert_primitive_with_exact_capacity(nested_element, &mut nested_list_vector.child(total_nested_capacity), current_offset + nested_idx, nested_element_type)?;
                    }

                    current_offset += nested_elements.len();
                } else {
                    nested_list_vector.set_null(nested_row_start + elem_idx);
                    current_offset += 1;
                }
            }

            // Set the entry for this row in the parent list using cumulative nested row tracking
            eprintln!("DEBUG: Setting list entry: row_index={}, nested_row_offset={}, nested_arrays_in_row={}",
                      row_index, nested_row_start, element_count);
            list_vector.set_entry(row_index, nested_row_start, element_count);

            // Update cumulative offsets with actual elements consumed
            *cumulative_offset = current_offset;
            *cumulative_nested_row += element_count; // Update nested row counter

        },
        _ => {
            // Primitives with exact capacity
            let total_capacity = get_exact_primitive_capacity(capacities, current_path)?;
            let mut flat_vector = list_vector.child(total_capacity);

            for (elem_idx, element) in elements.iter().enumerate() {
                insert_primitive_with_exact_capacity(element, &mut flat_vector, row_offset + elem_idx, element_type)?;
            }

            // Update cumulative offset with actual elements consumed
            *cumulative_offset += element_count;

        }
    };

    // For non-nested arrays, set the entry in the parent list
    match element_type {
        InferredJsonType::Array { .. } => {
            // Already handled above in the nested array case
        },
        _ => {
            // For primitive arrays, set entry with the row offset and count
            list_vector.set_entry(row_index, row_offset, element_count);
        }
    };

    Ok(())
}

/// Get exact nested capacity for a given path
pub fn get_exact_nested_capacity(capacities: &VectorCapacities, nested_path: &ProjectionPath) -> Result<usize, Box<dyn std::error::Error>> {
    let vector_path = projection_path_to_vector_path(nested_path);
    capacities.get_capacity(&vector_path)
        .ok_or_else(|| format!("Could not find nested capacity for path: {:?}", vector_path).into())
}

/// Get exact primitive capacity for a given path
pub fn get_exact_primitive_capacity(capacities: &VectorCapacities, current_path: &ProjectionPath) -> Result<usize, Box<dyn std::error::Error>> {
    let vector_path = projection_path_to_vector_path(current_path);
    capacities.get_capacity(&vector_path)
        .ok_or_else(|| format!("Could not find primitive capacity for path: {:?}", vector_path).into())
}

/// Insert primitive value with exact capacity
pub fn insert_primitive_with_exact_capacity(
    value: &TempJsonValue,
    flat_vector: &mut duckdb::core::FlatVector,
    index: usize,
    expected_type: &InferredJsonType,
) -> Result<(), Box<dyn std::error::Error>> {
    match (value, expected_type) {
        (TempJsonValue::Number(num), InferredJsonType::Number) => {
            let slice = flat_vector.as_mut_slice::<f64>();
            if index < slice.len() {
                slice[index] = *num;
            }
        },
        (TempJsonValue::String(s), InferredJsonType::String) => {
            flat_vector.insert(index, s.as_str());
        },
        (TempJsonValue::Boolean(b), InferredJsonType::Boolean) => {
            let slice = flat_vector.as_mut_slice::<bool>();
            if index < slice.len() {
                slice[index] = *b;
            }
        },
        (TempJsonValue::Null, _) => {
            flat_vector.set_null(index);
        },
        _ => {
            // Type mismatch - set as null
            flat_vector.set_null(index);
        }
    }
    Ok(())
}

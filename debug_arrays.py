#!/usr/bin/env python3

import json
import duckdb
import tempfile
import os

def test_array_dimension(data, description):
    """Test a specific array structure and print detailed results."""
    print(f"\n=== {description} ===")
    print(f"Input: {data}")
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(data, f)
        temp_file = f.name
    
    try:
        # Connect to DuckDB and load extension
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute('LOAD "build/debug/streaming_json_reader.duckdb_extension"')
        
        # Get schema
        schema = conn.execute(f'DESCRIBE SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
        print(f"Schema: {schema}")
        
        # Get data
        result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
        print(f"Result: {result}")
        
        # Analyze the result
        if result:
            array_data = result[0][0]
            print(f"Array data type: {type(array_data)}")
            if hasattr(array_data, '__len__'):
                print(f"Array length: {len(array_data)}")
                if len(array_data) > 0:
                    print(f"First element: {array_data[0]} (type: {type(array_data[0])})")
                    if hasattr(array_data[0], '__len__') and len(array_data[0]) > 0:
                        print(f"First sub-element: {array_data[0][0]} (type: {type(array_data[0][0])})")
                        if hasattr(array_data[0][0], '__len__') and len(array_data[0][0]) > 0:
                            print(f"First sub-sub-element: {array_data[0][0][0]} (type: {type(array_data[0][0][0])})")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        os.unlink(temp_file)

def main():
    print("=== Systematic Array Dimension Testing ===")
    
    # 1D array (baseline - should work)
    test_array_dimension([1, 2, 3], "1D Array of Numbers")
    
    # 2D array (should work)
    test_array_dimension([[1, 2], [3, 4]], "2D Array of Numbers")
    
    # 3D array (should work)
    test_array_dimension([[[1, 2]], [[3, 4]]], "3D Array of Numbers")
    
    # 4D array (fails)
    test_array_dimension([[[[1, 2]], [[3, 4]]]], "4D Array of Numbers")
    
    # 5D array (likely fails)
    test_array_dimension([[[[[1, 2]]]]], "5D Array of Numbers")
    
    # Single element arrays at different depths
    test_array_dimension([1], "1D Single Element")
    test_array_dimension([[1]], "2D Single Element")
    test_array_dimension([[[1]]], "3D Single Element")
    test_array_dimension([[[[1]]]], "4D Single Element")
    
    # Mixed structures
    test_array_dimension([[[1, 2], [3]], [[4]]], "3D Irregular Array")

if __name__ == "__main__":
    main()
